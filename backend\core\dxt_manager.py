"""
DXT (Desktop Extensions) 扩展管理模块
负责dxt文件的验证、解压、安装和配置管理
"""

import os
import json
import zipfile
import hashlib
import tempfile
import shutil
import logging
import time
import platform
from typing import Dict, List, Any, Optional, Tuple, Union
from pathlib import Path
from datetime import datetime
logger = logging.getLogger(__name__)

class DXTManager:
    """DXT扩展管理器"""
    
    # 支持的dxt文件最大大小 (50MB)
    MAX_DXT_SIZE = 50 * 1024 * 1024
    
    # 必需的manifest字段（根据DXT规范）
    REQUIRED_MANIFEST_FIELDS = ["dxt_version", "name", "version", "description", "author", "server"]
    
    def __init__(self, config_path: str, install_base_dir: str):
        """
        初始化DXT管理器
        
        Args:
            config_path: MCP配置文件路径
            install_base_dir: 扩展安装基础目录
        """
        self.config_path = Path(config_path)
        self.install_base_dir = Path(install_base_dir)
        
        # 确保安装目录存在
        self.install_base_dir.mkdir(parents=True, exist_ok=True)
        
        # 扩展安装目录
        self.extensions_dir = self.install_base_dir / "extensions"
        self.extensions_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"DXT管理器初始化完成 - 配置: {self.config_path}, 安装目录: {self.install_base_dir}")

    def _format_author_display(self, author_info: Any) -> str:
        """
        格式化author信息为显示字符串

        Args:
            author_info: author信息，可能是字符串或对象

        Returns:
            格式化后的author显示字符串
        """
        if isinstance(author_info, dict):
            author_name = author_info.get('name', '未知')
            author_email = author_info.get('email', '')
            if author_email:
                return f"{author_name} <{author_email}>"
            else:
                return author_name
        else:
            return str(author_info) if author_info else '未知'

    def _check_platform_compatibility(self, manifest: Dict[str, Any]) -> Tuple[bool, str]:
        """
        检查平台兼容性

        Args:
            manifest: DXT manifest内容

        Returns:
            (是否兼容, 错误信息)
        """
        try:
            # 获取当前系统平台
            current_platform = platform.system().lower()
            platform_mapping = {
                'windows': 'win32',
                'darwin': 'darwin',
                'linux': 'linux'
            }
            current_platform = platform_mapping.get(current_platform, current_platform)

            # 检查是否有兼容性配置
            compatibility = manifest.get('compatibility', {})
            supported_platforms = compatibility.get('platforms', [])

            # 如果没有指定平台，默认兼容所有平台
            if not supported_platforms:
                return True, ""

            # 检查当前平台是否在支持列表中
            if current_platform in supported_platforms:
                return True, ""
            else:
                supported_str = ', '.join(supported_platforms)
                return False, f"此扩展不支持当前平台 ({current_platform})，仅支持: {supported_str}"

        except Exception as e:
            logger.warning(f"检查平台兼容性时出错: {e}")
            # 出错时默认允许安装
            return True, ""

    def _extract_user_config_variables(self, mcp_config: Dict[str, Any]) -> List[str]:
        """
        提取MCP配置中的用户配置变量

        Args:
            mcp_config: MCP配置

        Returns:
            用户配置变量列表
        """
        import re
        variables = set()

        def extract_from_value(value):
            if isinstance(value, str):
                # 匹配 ${user_config.variable_name} 格式
                matches = re.findall(r'\$\{user_config\.([^}]+)\}', value)
                variables.update(matches)
            elif isinstance(value, list):
                for item in value:
                    extract_from_value(item)
            elif isinstance(value, dict):
                for v in value.values():
                    extract_from_value(v)

        extract_from_value(mcp_config)
        return sorted(list(variables))

    def _get_user_config_variables(self, manifest: Dict[str, Any]) -> List[str]:
        """
        获取扩展需要的用户配置变量

        Args:
            manifest: DXT manifest内容

        Returns:
            用户配置变量列表
        """
        try:
            server_config = manifest.get('server', {})
            mcp_config = server_config.get('mcp_config', {})
            return self._extract_user_config_variables(mcp_config)
        except Exception as e:
            logger.warning(f"提取用户配置变量时出错: {e}")
            return []

    def _get_user_config_requirements(self, manifest: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取扩展的完整用户配置需求信息

        Args:
            manifest: DXT manifest内容

        Returns:
            包含配置需求的字典，格式：
            {
                'required_variables': ['var1', 'var2'],  # 必需的变量
                'config_definitions': {
                    'var1': {
                        'type': 'string',
                        'title': 'API Key',
                        'description': 'Your API key',
                        'required': True,
                        'sensitive': True
                    },
                    ...
                }
            }
        """
        try:
            # 从mcp_config中提取引用的变量
            server_config = manifest.get('server', {})
            mcp_config = server_config.get('mcp_config', {})
            referenced_vars = set(self._extract_user_config_variables(mcp_config))

            # 获取user_config定义
            user_config_defs = manifest.get('user_config', {})

            logger.debug(f"配置需求分析 - 引用的变量: {referenced_vars}")
            logger.debug(f"配置需求分析 - user_config定义: {list(user_config_defs.keys())}")

            # 构建配置需求信息
            config_definitions = {}
            required_variables = []

            # 处理在mcp_config中引用的变量
            for var_name in referenced_vars:
                if var_name in user_config_defs:
                    # 变量在user_config中有定义
                    var_def = user_config_defs[var_name]
                    config_definitions[var_name] = var_def.copy()

                    # 判断是否必需：required=True 且没有默认值
                    is_required = (
                        var_def.get('required', False) and
                        'default' not in var_def
                    )
                    if is_required:
                        required_variables.append(var_name)
                else:
                    # 变量在mcp_config中引用但在user_config中没有定义
                    # 创建一个基本定义并标记为必需
                    config_definitions[var_name] = {
                        'type': 'string',
                        'title': var_name.replace('_', ' ').title(),
                        'description': f'Configuration value for {var_name}',
                        'required': True
                    }
                    required_variables.append(var_name)

            # 处理在user_config中定义但未在mcp_config中引用的变量
            # 这些可能是可选配置或有默认值的配置
            for var_name, var_def in user_config_defs.items():
                if var_name not in referenced_vars:
                    config_definitions[var_name] = var_def.copy()
                    # 只有明确标记为required且没有默认值的才加入必需列表
                    if var_def.get('required', False) and 'default' not in var_def:
                        required_variables.append(var_name)

            return {
                'required_variables': sorted(required_variables),
                'config_definitions': config_definitions
            }

        except Exception as e:
            logger.warning(f"获取用户配置需求时出错: {e}")
            return {
                'required_variables': [],
                'config_definitions': {}
            }

    def _replace_user_config_variables(self, config: Dict[str, Any], user_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        替换配置中的用户配置变量

        Args:
            config: MCP配置
            user_config: 用户配置变量字典

        Returns:
            替换后的配置
        """
        import re
        import copy

        # 深拷贝配置以避免修改原始数据
        result = copy.deepcopy(config)

        def replace_in_value(value):
            if isinstance(value, str):
                # 替换 ${user_config.variable_name} 格式的变量
                def replace_var(match):
                    var_name = match.group(1)
                    if var_name in user_config:
                        config_value = user_config[var_name]
                        # 根据DXT规范，将不同类型的值转换为字符串
                        if isinstance(config_value, bool):
                            return 'true' if config_value else 'false'
                        elif isinstance(config_value, list):
                            # 数组类型转换为逗号分隔的字符串
                            return ','.join(str(item) for item in config_value)
                        else:
                            return str(config_value)
                    else:
                        return match.group(0)  # 如果找不到变量，保持原样

                return re.sub(r'\$\{user_config\.([^}]+)\}', replace_var, value)
            elif isinstance(value, list):
                return [replace_in_value(item) for item in value]
            elif isinstance(value, dict):
                return {k: replace_in_value(v) for k, v in value.items()}
            else:
                return value

        return replace_in_value(result)

    def _safe_remove_file(self, file_path: Path, max_retries: int = 3, delay: float = 0.1) -> bool:
        """
        安全删除文件，处理Windows文件锁定问题

        Args:
            file_path: 要删除的文件路径
            max_retries: 最大重试次数
            delay: 重试间隔（秒）

        Returns:
            是否成功删除
        """
        for attempt in range(max_retries):
            try:
                if file_path.exists():
                    file_path.unlink()
                return True
            except (PermissionError, OSError) as e:
                if attempt < max_retries - 1:
                    logger.debug(f"删除文件失败，第{attempt + 1}次重试: {e}")
                    time.sleep(delay)
                else:
                    logger.warning(f"删除文件失败，已达最大重试次数: {e}")
                    return False
        return False

    def validate_dxt_file(self, file_path: Path) -> Tuple[bool, str]:
        """
        验证dxt文件的完整性和格式
        
        Args:
            file_path: dxt文件路径
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            # 检查文件存在
            if not file_path.exists():
                return False, "文件不存在"
            
            # 检查文件大小
            if file_path.stat().st_size > self.MAX_DXT_SIZE:
                return False, f"文件大小超过限制 ({self.MAX_DXT_SIZE // 1024 // 1024}MB)"
            
            # 检查文件扩展名
            if file_path.suffix.lower() != '.dxt':
                return False, "文件扩展名必须为.dxt"

            # 检查ZIP文件内容（直接尝试打开，避免 zipfile.is_zipfile 的文件句柄问题）
            try:
                with zipfile.ZipFile(file_path, 'r') as zip_file:
                    # 检查是否包含manifest.json
                    if 'manifest.json' not in zip_file.namelist():
                        return False, "缺少manifest.json文件"

                    # 检查危险路径
                    for name in zip_file.namelist():
                        if self._is_dangerous_path(name):
                            return False, f"包含危险路径: {name}"

                    # 验证manifest.json格式
                    try:
                        manifest_content = zip_file.read('manifest.json')
                        manifest = json.loads(manifest_content.decode('utf-8'))

                        # 检查必需字段
                        for field in self.REQUIRED_MANIFEST_FIELDS:
                            if field not in manifest:
                                return False, f"manifest.json缺少必需字段: {field}"

                        # 验证author字段结构
                        if not isinstance(manifest.get('author'), dict):
                            return False, "author字段必须是对象"
                        if 'name' not in manifest['author']:
                            return False, "author.name字段是必需的"

                        # 验证server字段结构
                        if not isinstance(manifest.get('server'), dict):
                            return False, "server字段必须是对象"
                        server_required_fields = ['type', 'entry_point', 'mcp_config']
                        for field in server_required_fields:
                            if field not in manifest['server']:
                                return False, f"server.{field}字段是必需的"

                        # 验证server.type值
                        valid_server_types = ['python', 'node', 'binary']
                        if manifest['server']['type'] not in valid_server_types:
                            return False, f"server.type必须是以下值之一: {', '.join(valid_server_types)}"

                        # 验证名称格式
                        if not self._is_valid_extension_name(manifest['name']):
                            return False, "扩展名称格式无效(只允许字母、数字、短划线、下划线)"

                        # 检查平台兼容性
                        is_compatible, compatibility_error = self._check_platform_compatibility(manifest)
                        if not is_compatible:
                            return False, compatibility_error

                    except json.JSONDecodeError as e:
                        return False, f"manifest.json格式错误: {str(e)}"
                    except UnicodeDecodeError:
                        return False, "manifest.json编码错误，必须为UTF-8"
            except zipfile.BadZipFile:
                return False, "不是有效的dxt文件格式(ZIP)"
            except Exception as e:
                logger.error(f"读取ZIP文件时出错: {e}")
                return False, f"文件读取失败: {str(e)}"
            
            return True, "验证通过"
            
        except Exception as e:
            logger.error(f"验证dxt文件时出错: {e}")
            return False, f"验证失败: {str(e)}"
    
    def extract_manifest(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """
        提取dxt文件的manifest配置
        
        Args:
            file_path: dxt文件路径
            
        Returns:
            manifest配置字典，失败返回None
        """
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                manifest_content = zip_file.read('manifest.json')
                manifest = json.loads(manifest_content.decode('utf-8'))
                
                # 添加文件元信息
                manifest['_meta'] = {
                    'file_path': str(file_path),
                    'file_size': file_path.stat().st_size,
                    'extracted_at': datetime.now().isoformat(),
                    'file_hash': self._calculate_file_hash(file_path)
                }
                
                return manifest
                
        except Exception as e:
            logger.error(f"提取manifest失败: {e}")
            return None

    def check_extension_exists(self, extension_name: str) -> bool:
        """
        检查扩展是否已存在

        Args:
            extension_name: 扩展名称

        Returns:
            是否已存在
        """
        extension_dir = self.install_base_dir / "extensions" / extension_name
        return extension_dir.exists()

    def install_extension(self, file_path: Path, force_update: bool = False, user_config: Optional[Dict[str, Union[str, int, float, bool, List[str]]]] = None) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        安装dxt扩展
        
        Args:
            file_path: dxt文件路径
            force_update: 是否强制更新已存在的扩展
            
        Returns:
            (是否成功, 消息, 扩展信息)
        """
        try:
            logger.info(f"DXTManager.install_extension 开始: file_path={file_path}, force_update={force_update}, user_config={user_config}")
            # 验证文件
            is_valid, error_msg = self.validate_dxt_file(file_path)
            if not is_valid:
                return False, f"文件验证失败: {error_msg}", None

            # 提取manifest
            manifest = self.extract_manifest(file_path)
            if not manifest:
                return False, "无法读取扩展配置", None

            extension_name = manifest['name']

            # 检查是否需要用户配置变量
            config_requirements = self._get_user_config_requirements(manifest)
            all_config_vars = list(config_requirements['config_definitions'].keys())
            required_vars = config_requirements['required_variables']

            logger.info(f"扩展 {extension_name} 配置需求分析:")
            logger.info(f"  所有配置变量: {all_config_vars}")
            logger.info(f"  必需变量: {required_vars}")
            logger.info(f"  用户配置: {user_config}")

            # 如果有任何用户配置定义，且没有提供用户配置，则显示配置表单
            if all_config_vars and not user_config:
                logger.info(f"扩展 {extension_name} 有用户配置定义，显示配置表单")
                # 返回特殊状态，表示需要用户配置
                return False, "NEED_USER_CONFIG", {
                    'name': extension_name,
                    'required_variables': required_vars,  # 只有必需的变量会被标记为必填
                    'all_variables': all_config_vars,     # 所有变量都会显示在表单中
                    'config_definitions': config_requirements['config_definitions'],
                    'manifest': manifest
                }
            elif all_config_vars and user_config:
                # 检查是否有未提供的必需配置
                missing_required_vars = []
                for var_name in required_vars:
                    if var_name not in user_config or user_config[var_name] in [None, '', []]:
                        missing_required_vars.append(var_name)

                if missing_required_vars:
                    logger.info(f"扩展 {extension_name} 缺失必需配置: {missing_required_vars}")
                    return False, "NEED_USER_CONFIG", {
                        'name': extension_name,
                        'required_variables': required_vars,
                        'all_variables': all_config_vars,
                        'config_definitions': config_requirements['config_definitions'],
                        'manifest': manifest
                    }
                else:
                    logger.info(f"扩展 {extension_name} 所有必需配置都已提供，继续安装")
            else:
                logger.info(f"扩展 {extension_name} 无用户配置定义，直接安装")
            extension_dir = self.extensions_dir / extension_name
            
            # 检查是否已安装
            if extension_dir.exists() and not force_update:
                return False, f"扩展 {extension_name} 已存在，使用force_update=True强制更新", None
            
            # 创建临时目录进行解压
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # 解压到临时目录
                with zipfile.ZipFile(file_path, 'r') as zip_file:
                    zip_file.extractall(temp_path)
                
                # 如果目标目录存在，先删除
                if extension_dir.exists():
                    shutil.rmtree(extension_dir)
                
                # 移动到最终位置
                shutil.move(str(temp_path), str(extension_dir))
                
                logger.info(f"扩展 {extension_name} 安装成功: {extension_dir}")
            
            # 更新MCP配置
            mcp_config = self._generate_mcp_config(manifest, extension_dir, user_config)
            if mcp_config:
                success = self._update_mcp_configuration(extension_name, mcp_config)
                if success:
                    logger.info(f"扩展 {extension_name} 的MCP配置已添加到 {self.config_path}")
                    # 尝试启动MCP服务器
                    try:
                        self._start_mcp_server(extension_name)
                    except Exception as e:
                        logger.warning(f"扩展 {extension_name} MCP服务器启动失败: {e}")
                else:
                    logger.warning(f"扩展 {extension_name} 安装成功，但MCP配置更新失败")
            
            # 处理author字段
            author_display = self._format_author_display(manifest.get('author', '未知'))

            # 保存扩展信息
            extension_info = {
                'name': extension_name,
                'version': manifest.get('version', '未知'),
                'description': manifest.get('description', ''),
                'author': author_display,
                'author_info': manifest.get('author', {}),  # 保留完整的author信息供详情页使用
                'installed_at': datetime.now().isoformat(),
                'install_path': str(extension_dir),
                'manifest': manifest,
                'enabled': True,
                'current_config': user_config or {}  # 保存当前用户配置
            }
            
            self._save_extension_info(extension_name, extension_info)
            
            return True, f"扩展 {extension_name} v{manifest.get('version', '未知')} 安装成功", extension_info
            
        except Exception as e:
            logger.error(f"安装扩展失败: {e}")
            return False, f"安装失败: {str(e)}", None
    
    def uninstall_extension(self, extension_name: str) -> Tuple[bool, str]:
        """
        卸载扩展
        
        Args:
            extension_name: 扩展名称
            
        Returns:
            (是否成功, 消息)
        """
        try:
            extension_dir = self.extensions_dir / extension_name
            
            if not extension_dir.exists():
                return False, f"扩展 {extension_name} 不存在"
            
            # 停止并从MCP配置中移除
            try:
                self._stop_mcp_server(extension_name)
            except Exception as e:
                logger.warning(f"停止MCP服务器失败: {e}")

            self._remove_from_mcp_configuration(extension_name)
            
            # 删除扩展目录
            shutil.rmtree(extension_dir)
            
            # 删除扩展信息
            self._remove_extension_info(extension_name)
            
            logger.info(f"扩展 {extension_name} 卸载成功")
            return True, f"扩展 {extension_name} 已卸载"
            
        except Exception as e:
            logger.error(f"卸载扩展失败: {e}")
            return False, f"卸载失败: {str(e)}"
    
    def list_installed_extensions(self) -> List[Dict[str, Any]]:
        """
        获取已安装扩展列表
        
        Returns:
            扩展信息列表
        """
        extensions = []
        
        try:
            if not self.extensions_dir.exists():
                return extensions
            
            for ext_dir in self.extensions_dir.iterdir():
                if ext_dir.is_dir():
                    extension_info = self._load_extension_info(ext_dir.name)
                    if extension_info:
                        extensions.append(extension_info)
                    else:
                        # 尝试从manifest重建信息
                        manifest_path = ext_dir / "manifest.json"
                        if manifest_path.exists():
                            try:
                                with open(manifest_path, 'r', encoding='utf-8') as f:
                                    manifest = json.load(f)
                                
                                # 处理author字段
                                author_display = self._format_author_display(manifest.get('author', '未知'))

                                extension_info = {
                                    'name': ext_dir.name,
                                    'version': manifest.get('version', '未知'),
                                    'description': manifest.get('description', ''),
                                    'author': author_display,
                                    'author_info': manifest.get('author', {}),
                                    'installed_at': '未知',
                                    'install_path': str(ext_dir),
                                    'manifest': manifest,
                                    'enabled': True,
                                    'current_config': {},  # 重建时使用空配置
                                    'rebuilt': True  # 标记为重建的信息
                                }
                                extensions.append(extension_info)
                                
                            except Exception as e:
                                logger.warning(f"无法读取扩展 {ext_dir.name} 的配置: {e}")
        
        except Exception as e:
            logger.error(f"获取扩展列表失败: {e}")
        
        return extensions
    
    def enable_extension(self, extension_name: str, enabled: bool = True) -> Tuple[bool, str]:
        """
        启用或禁用扩展
        
        Args:
            extension_name: 扩展名称
            enabled: 是否启用
            
        Returns:
            (是否成功, 消息)
        """
        try:
            extension_info = self._load_extension_info(extension_name)
            if not extension_info:
                return False, f"扩展 {extension_name} 不存在"
            
            # 更新扩展信息
            extension_info['enabled'] = enabled
            self._save_extension_info(extension_name, extension_info)
            
            # 更新MCP配置
            if enabled:
                extension_dir = Path(extension_info['install_path'])
                current_config = extension_info.get('current_config', {})
                mcp_config = self._generate_mcp_config(extension_info['manifest'], extension_dir, current_config)
                if mcp_config:
                    self._update_mcp_configuration(extension_name, mcp_config)
            else:
                self._remove_from_mcp_configuration(extension_name)
            
            action = "启用" if enabled else "禁用"
            return True, f"扩展 {extension_name} 已{action}"
            
        except Exception as e:
            logger.error(f"切换扩展状态失败: {e}")
            return False, f"操作失败: {str(e)}"

    def reconfigure_extension(self, extension_name: str, user_config: Dict[str, Union[str, int, float, bool, List[str]]]) -> Tuple[bool, str]:
        """
        重新配置扩展的用户配置

        Args:
            extension_name: 扩展名称
            user_config: 新的用户配置

        Returns:
            (是否成功, 消息)
        """
        try:
            # 获取扩展信息
            extension_info = self._load_extension_info(extension_name)
            if not extension_info:
                return False, f"扩展 {extension_name} 不存在"

            manifest = extension_info.get('manifest')
            if not manifest:
                return False, f"扩展 {extension_name} 的manifest信息缺失"

            # 验证用户配置
            config_requirements = self._get_user_config_requirements(manifest)
            required_vars = config_requirements['required_variables']

            # 检查必需配置是否都已提供
            missing_required_vars = []
            for var_name in required_vars:
                if var_name not in user_config or user_config[var_name] in [None, '', []]:
                    missing_required_vars.append(var_name)

            if missing_required_vars:
                return False, f"缺少必需的配置项: {', '.join(missing_required_vars)}"

            # 更新扩展信息中的当前配置
            extension_info['current_config'] = user_config
            self._save_extension_info(extension_name, extension_info)

            # 重新生成MCP配置
            extension_dir = Path(extension_info['install_path'])
            mcp_config = self._generate_mcp_config(manifest, extension_dir, user_config)

            if mcp_config and extension_info.get('enabled', True):
                # 只有在扩展启用时才更新MCP配置
                success = self._update_mcp_configuration(extension_name, mcp_config)
                if success:
                    logger.info(f"扩展 {extension_name} 的配置已更新")
                    return True, f"扩展 {extension_name} 配置更新成功"
                else:
                    return False, "MCP配置更新失败"
            else:
                # 扩展被禁用或没有MCP配置，只更新扩展信息
                logger.info(f"扩展 {extension_name} 的配置已保存（扩展当前被禁用）")
                return True, f"扩展 {extension_name} 配置已保存"

        except Exception as e:
            logger.error(f"重新配置扩展失败: {e}")
            return False, f"配置更新失败: {str(e)}"

    def _extract_current_user_config(self, extension_name: str, manifest: Dict[str, Any]) -> Dict[str, Any]:
        """
        从MCP配置中提取当前的用户配置值

        Args:
            extension_name: 扩展名称
            manifest: 扩展manifest

        Returns:
            当前用户配置字典
        """
        try:
            # 加载MCP配置
            mcp_config_data = self._load_mcp_configuration()
            mcp_servers = mcp_config_data.get('mcpServers', {})

            if extension_name not in mcp_servers:
                return {}

            server_config = mcp_servers[extension_name]
            user_config_defs = manifest.get('user_config', {})
            current_config = {}

            # 遍历用户配置定义，从MCP配置中提取当前值
            for var_name, config_def in user_config_defs.items():
                current_value = self._extract_config_value_from_mcp(server_config, var_name, config_def)
                if current_value is not None:
                    current_config[var_name] = current_value

            return current_config

        except Exception as e:
            logger.error(f"提取当前用户配置失败: {e}")
            return {}

    def _extract_config_value_from_mcp(self, server_config: Dict[str, Any], var_name: str, config_def: Dict[str, Any]) -> Any:
        """
        从MCP服务器配置中提取特定变量的当前值

        Args:
            server_config: MCP服务器配置
            var_name: 变量名
            config_def: 变量定义

        Returns:
            当前配置值，如果未找到则返回None
        """
        import re

        # 构建要搜索的模式
        pattern = f"\\${{user_config\\.{re.escape(var_name)}}}"

        def search_in_value(value):
            if isinstance(value, str):
                # 检查是否包含该变量的占位符
                if re.search(pattern, value):
                    # 尝试从替换后的值中反推原始配置值
                    # 这里简化处理，实际应该更复杂
                    return self._reverse_extract_value(value, pattern, config_def)
            elif isinstance(value, list):
                for item in value:
                    result = search_in_value(item)
                    if result is not None:
                        return result
            elif isinstance(value, dict):
                for v in value.values():
                    result = search_in_value(v)
                    if result is not None:
                        return result
            return None

        # 在整个服务器配置中搜索
        return search_in_value(server_config)

    def _reverse_extract_value(self, replaced_value: str, pattern: str, config_def: Dict[str, Any]) -> Any:
        """
        从替换后的值中反推原始配置值（简化实现）

        Args:
            replaced_value: 替换后的值
            pattern: 变量模式
            config_def: 变量定义

        Returns:
            原始配置值
        """
        # 这是一个简化的实现，实际情况可能更复杂
        # 对于简单情况，如果整个字符串就是一个变量替换，我们可以返回当前值
        var_type = config_def.get('type', 'string')

        if var_type == 'boolean':
            return replaced_value.lower() == 'true'
        elif var_type == 'number':
            try:
                return float(replaced_value) if '.' in replaced_value else int(replaced_value)
            except ValueError:
                return None
        elif var_type == 'array':
            return replaced_value.split(',') if replaced_value else []
        else:
            return replaced_value

    def _is_dangerous_path(self, path: str) -> bool:
        """检查路径是否包含危险字符"""
        # 防护路径遍历攻击
        if '..' in path or path.startswith('/') or ':' in path:
            return True
        
        # 检查Windows保留名称
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL'] + [f'COM{i}' for i in range(1, 10)] + [f'LPT{i}' for i in range(1, 10)]
        path_parts = Path(path).parts
        for part in path_parts:
            if part.upper() in reserved_names:
                return True
        
        return False
    
    def _is_valid_extension_name(self, name: str) -> bool:
        """验证扩展名称格式"""
        import re
        # 只允许字母、数字、短划线、下划线，长度3-50
        pattern = r'^[a-zA-Z0-9_-]{3,50}$'
        return bool(re.match(pattern, name))
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _generate_mcp_config(self, manifest: Dict[str, Any], extension_dir: Path, user_config: Optional[Dict[str, Union[str, int, float, bool, List[str]]]] = None) -> Optional[Dict[str, Any]]:
        """
        根据manifest生成MCP服务器配置
        
        Args:
            manifest: 扩展manifest
            extension_dir: 扩展安装目录
            
        Returns:
            MCP配置字典，失败返回None
        """
        try:
            # 检查是否包含MCP配置（根据DXT规范）
            server_config = manifest.get('server', {})
            mcp_config = server_config.get('mcp_config', {})
            if not mcp_config:
                return None
            
            # 构建基础配置
            config = {
                'enabled': True,
                'description': f"DXT扩展: {manifest.get('description', '')}",
                'isActive': False,
                'source': 'dxt'  # 标记为DXT扩展
            }
            
            # 处理不同类型的MCP服务器
            if 'command' in mcp_config:
                # stdio类型
                config.update({
                    'command': mcp_config['command'],
                    'args': mcp_config.get('args', []),
                    'env': mcp_config.get('env', {})
                })
                
                # 替换DXT规范的路径占位符（使用正确的路径分隔符）
                extension_dir_str = str(extension_dir).replace('\\', '/')

                if '${__dirname}' in str(config['command']):
                    config['command'] = str(config['command']).replace('${__dirname}', extension_dir_str)

                config['args'] = [
                    str(arg).replace('${__dirname}', extension_dir_str)
                    for arg in config['args']
                ]

                # 处理环境变量中的路径占位符
                if config.get('env'):
                    for key, value in config['env'].items():
                        if isinstance(value, str) and '${__dirname}' in value:
                            config['env'][key] = value.replace('${__dirname}', extension_dir_str)

            elif 'url' in mcp_config:
                # SSE类型
                config['url'] = mcp_config['url']

            # 替换用户配置变量
            if user_config:
                config = self._replace_user_config_variables(config, user_config)
            
            return config
            
        except Exception as e:
            logger.error(f"生成MCP配置失败: {e}")
            return None
    
    def _update_mcp_configuration(self, extension_name: str, mcp_config: Dict[str, Any]) -> bool:
        """更新MCP配置文件"""
        try:
            # 读取现有配置
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {'mcpServers': {}}
            
            # 确保mcpServers存在
            if 'mcpServers' not in config:
                config['mcpServers'] = {}
            
            # 添加或更新扩展配置
            config['mcpServers'][extension_name] = mcp_config
            
            # 写回配置文件
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            # 只更新MCP客户端配置，不重启所有服务器
            try:
                self._update_mcp_client_config(config)
            except Exception as e:
                logger.warning(f"更新MCP客户端配置失败: {e}")

            logger.info(f"MCP配置已更新: {extension_name}")
            return True
            
        except Exception as e:
            logger.error(f"更新MCP配置失败: {e}")
            return False

    def _load_mcp_configuration(self) -> Dict[str, Any]:
        """加载MCP配置文件"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {'mcpServers': {}}
        except Exception as e:
            logger.error(f"加载MCP配置失败: {e}")
            return {'mcpServers': {}}

    def _reload_mcp_servers(self, config: Dict[str, Any]) -> None:
        """重新加载MCP服务器配置"""
        try:
            # 导入MCP服务器管理器
            from backend.mcp.server_manager import mcp_server

            # 更新服务器配置
            mcp_server.client.servers_config = config['mcpServers']
            mcp_server.client.save_config()

            # 重新启动所有服务器
            import asyncio
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，使用run_coroutine_threadsafe
                future = asyncio.run_coroutine_threadsafe(
                    self._reload_servers_async(),
                    loop
                )
                future.result(timeout=30.0)
            else:
                # 如果事件循环未运行，直接运行
                asyncio.run(self._reload_servers_async())

            logger.info("MCP服务器配置已重新加载")

        except Exception as e:
            logger.error(f"重新加载MCP服务器失败: {e}")
            raise

    async def _reload_servers_async(self) -> None:
        """异步重新加载服务器"""
        try:
            from backend.mcp.server_manager import mcp_server

            # 确保事件循环运行
            mcp_server._ensure_loop_running()

            # 重新启动服务器
            success = mcp_server.start_servers_sync(timeout=60.0)
            if not success:
                logger.warning("部分MCP服务器启动失败")

        except Exception as e:
            logger.error(f"异步重新加载服务器失败: {e}")
            raise

    def _update_mcp_client_config(self, config: Dict[str, Any]) -> None:
        """只更新MCP客户端配置，不重启服务器"""
        try:
            # 导入MCP服务器管理器
            from backend.mcp.server_manager import mcp_server

            # 更新服务器配置
            mcp_server.client.servers_config = config['mcpServers']
            mcp_server.client.save_config()

            logger.info("MCP客户端配置已更新（未重启服务器）")

        except Exception as e:
            logger.error(f"更新MCP客户端配置失败: {e}")
            raise

    def _start_mcp_server(self, extension_name: str) -> bool:
        """
        启动DXT扩展的MCP服务器

        Args:
            extension_name: 扩展名称

        Returns:
            是否成功启动
        """
        try:
            # 导入MCP服务器管理器
            from backend.mcp.server_manager import mcp_server

            # 检查服务器是否已配置
            servers = mcp_server.get_servers()
            if extension_name not in servers:
                logger.warning(f"MCP服务器 {extension_name} 未在配置中找到")
                return False

            # 启动服务器
            success = mcp_server.start_server_sync(extension_name, timeout=30.0)
            if success:
                logger.info(f"DXT扩展 {extension_name} 的MCP服务器启动成功")
            else:
                logger.warning(f"DXT扩展 {extension_name} 的MCP服务器启动失败")

            return success

        except Exception as e:
            logger.error(f"启动DXT扩展MCP服务器失败: {e}")
            return False

    def _stop_mcp_server(self, extension_name: str) -> bool:
        """
        停止DXT扩展的MCP服务器

        Args:
            extension_name: 扩展名称

        Returns:
            是否成功停止
        """
        try:
            # 导入MCP服务器管理器
            from backend.mcp.server_manager import mcp_server

            # 检查服务器是否已配置
            servers = mcp_server.get_servers()
            if extension_name not in servers:
                logger.debug(f"MCP服务器 {extension_name} 未在配置中找到")
                return True

            # 停止服务器
            import asyncio
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，使用run_coroutine_threadsafe
                future = asyncio.run_coroutine_threadsafe(
                    mcp_server.client.stop_server(extension_name),
                    loop
                )
                success = future.result(timeout=10.0)
            else:
                # 如果事件循环未运行，直接运行
                success = asyncio.run(mcp_server.client.stop_server(extension_name))

            if success:
                logger.info(f"DXT扩展 {extension_name} 的MCP服务器停止成功")
            else:
                logger.warning(f"DXT扩展 {extension_name} 的MCP服务器停止失败")

            return success

        except Exception as e:
            logger.error(f"停止DXT扩展MCP服务器失败: {e}")
            return False

    def uninstall_extension_by_mcp_name(self, mcp_server_name: str) -> Tuple[bool, str]:
        """
        通过MCP服务器名称卸载DXT扩展

        Args:
            mcp_server_name: MCP服务器名称（通常与扩展名相同）

        Returns:
            (是否成功, 消息)
        """
        try:
            # 检查MCP配置中是否存在该服务器且标记为DXT扩展
            config = self._load_mcp_configuration()
            servers = config.get('mcpServers', {})

            if mcp_server_name not in servers:
                return False, f"MCP服务器 '{mcp_server_name}' 不存在"

            server_config = servers[mcp_server_name]
            if server_config.get('source') != 'dxt':
                return False, f"'{mcp_server_name}' 不是DXT扩展"

            # 使用扩展名进行卸载（MCP服务器名称通常与扩展名相同）
            return self.uninstall_extension(mcp_server_name)

        except Exception as e:
            logger.error(f"通过MCP名称卸载扩展失败: {e}")
            return False, f"卸载失败: {str(e)}"
    
    def _remove_from_mcp_configuration(self, extension_name: str) -> bool:
        """从MCP配置中移除扩展"""
        try:
            if not self.config_path.exists():
                return True
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if 'mcpServers' in config and extension_name in config['mcpServers']:
                del config['mcpServers'][extension_name]
                
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)

                # 只更新MCP客户端的配置，不重启所有服务器
                try:
                    self._update_mcp_client_config(config)
                except Exception as e:
                    logger.warning(f"更新MCP客户端配置失败: {e}")

                logger.info(f"已从MCP配置移除: {extension_name}")

            return True
            
        except Exception as e:
            logger.error(f"从MCP配置移除扩展失败: {e}")
            return False
    
    def _get_extension_info_path(self, extension_name: str) -> Path:
        """获取扩展信息文件路径"""
        return self.extensions_dir / extension_name / ".extension_info.json"
    
    def _save_extension_info(self, extension_name: str, info: Dict[str, Any]) -> bool:
        """保存扩展信息"""
        try:
            info_path = self._get_extension_info_path(extension_name)
            info_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(info, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            logger.error(f"保存扩展信息失败: {e}")
            return False
    
    def _load_extension_info(self, extension_name: str) -> Optional[Dict[str, Any]]:
        """加载扩展信息"""
        try:
            info_path = self._get_extension_info_path(extension_name)
            if info_path.exists():
                with open(info_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
            
        except Exception as e:
            logger.error(f"加载扩展信息失败: {e}")
            return None
    
    def _remove_extension_info(self, extension_name: str) -> bool:
        """删除扩展信息"""
        try:
            info_path = self._get_extension_info_path(extension_name)
            if info_path.exists():
                info_path.unlink()
            return True
            
        except Exception as e:
            logger.error(f"删除扩展信息失败: {e}")
            return False 