import React, { useState, useEffect } from 'react';
import { 
  Typo<PERSON>, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Space, 
  Divider,
  Tabs,
  Switch,
  Badge,
  Alert,
  Spin,
  Tag,
  Empty,
  Tooltip
} from 'antd';
import { 
  PlusOutlined, 
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ExclamationCircleOutlined,
  EditOutlined,
  SaveOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  AppstoreOutlined,
  FileAddOutlined
} from '@ant-design/icons';
import './MCPSettings.css';
import { getMCPServers, addMCPServer, removeMCPServer, toggleMCPServer, getMCPServersStatus, updateMCPServer } from '../../mcp';
import MonacoEditor from '../MonacoEditor';
import { MessageService } from '../../utils/MessageService';
import MCPMarketplace from '../MCPMarketplace';
import DXTUpload from '../DXTUpload';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

const MCPSettings = ({ onUpdateMcpConfig }) => {
  // 使用Modal.useModal钩子获取modal实例和contextHolder
  const [modal, contextHolder] = Modal.useModal();
  
  const [servers, setServers] = useState([]);
  const [serversStatus, setServersStatus] = useState({});
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentServer, setCurrentServer] = useState(null);
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [serverType, setServerType] = useState('stdio');
  const [editServerType, setEditServerType] = useState('stdio');
  
  // 新增MCP配置编辑相关状态
  const [mcpConfigVisible, setMcpConfigVisible] = useState(false);
  const [mcpConfig, setMcpConfig] = useState('');
  
  // 添加MCP配置相关的状态管理
  const [configLoading, setConfigLoading] = useState(false);
  const [configSaving, setConfigSaving] = useState(false);
  const [configValidating, setConfigValidating] = useState(false);
  const [configError, setConfigError] = useState(null);

  // DXT扩展重新配置相关状态
  const [reconfigureModalVisible, setReconfigureModalVisible] = useState(false);
  const [reconfigureExtensionName, setReconfigureExtensionName] = useState('');
  const [reconfigureManifest, setReconfigureManifest] = useState(null);
  const [reconfigureCurrentConfig, setReconfigureCurrentConfig] = useState({});
  const [reconfigureForm] = Form.useForm();
  const [configValid, setConfigValid] = useState(true);
  
  // 新增插件市场状态
  const [marketplaceVisible, setMarketplaceVisible] = useState(false);
  
  // DXT相关状态管理
  const [dxtUploadVisible, setDxtUploadVisible] = useState(false);
  const [dxtRefreshTrigger, setDxtRefreshTrigger] = useState(0);

  // 加载MCP服务器列表和状态
  useEffect(() => {
    fetchServers();
    fetchServersStatus();
  }, []);

  // 获取MCP服务器列表
  const fetchServers = async () => {
    setLoading(true);
    try {
      const serversData = await getMCPServers();
      
      // 将对象转换为数组
      const serverList = Object.entries(serversData || {}).map(([name, config]) => ({
        name,
        type: config.url ? 'sse' : 'stdio',
        config,
        enabled: config.enabled !== false
      }));
      fetchServersStatus();
      setServers(serverList);
    } catch (error) {
      console.error('获取MCP服务器列表失败:', error);
      MessageService.error('获取MCP服务器列表失败');
    } finally {
      setLoading(false);
    }
  };
  
  // 获取MCP服务器状态
  const fetchServersStatus = async () => {
    try {
      const status = await getMCPServersStatus();
      setServersStatus(status);
    } catch (error) {
      console.error('获取MCP服务器状态失败:', error);
    }
  };

  // 添加MCP服务器
  const handleAddServer = async (values) => {
    setLoading(true);
    try {
      const config = values.type === 'stdio' 
        ? { 
            command: values.command,
            args: values.args ? values.args.split(/[\s\n]+/).filter(arg => arg.trim()) : [],
            env: values.env ? values.env.split(/[\s\n]+/).filter(env => env.trim()) : {}
          }
        : { 
            url: values.url 
          };
      
      const result = await addMCPServer(values.name, values.type, config, values.description);
      
      if (result.success) {
        MessageService.success(`MCP服务器 ${values.name} 已添加`);
        setModalVisible(false);
        form.resetFields();
        fetchServers();
      } else {
        MessageService.error(result.message || '添加MCP服务器失败');
      }
    } catch (error) {
      console.error('添加MCP服务器失败:', error);
      MessageService.error('添加MCP服务器失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除MCP服务器
  const handleDeleteServer = (serverName) => {
    console.log('serverName', serverName);
    // 使用modal.confirm替代Modal.confirm，解决警告问题
    modal.confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除MCP服务器 ${serverName} 吗？`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        setLoading(true);
        try {
          console.log(`正在删除MCP服务器: ${serverName}`);
          const result = await removeMCPServer(serverName);
          
          if (result.success) {
            MessageService.success(`MCP服务器 ${serverName} 已删除`);
            // 更新本地状态
            setServers(prev => prev.filter(server => server.name !== serverName));
            // 也更新状态缓存
            setServersStatus(prev => {
              const newStatus = { ...prev };
              delete newStatus[serverName];
              return newStatus;
            });
          } else {
            console.error(`删除MCP服务器 ${serverName} 失败:`, result.message || '未知错误');
            MessageService.error(result.message || '删除MCP服务器失败');
          }
        } catch (error) {
          console.error(`删除MCP服务器 ${serverName} 过程中发生异常:`, error);
          MessageService.error(`删除MCP服务器失败: ${error.message || '未知错误'}`);
        } finally {
          setLoading(false);
        }
      },
      onCancel() {
        console.log('取消删除MCP服务器');
      },
    });
  };

  // 启用/禁用MCP服务器
  const handleToggleServer = async (name, enabled) => {
    setLoading(true);
    try {
      const result = await toggleMCPServer(name, enabled);
      
      if (result.success) {
        MessageService.success(`MCP服务器 ${name} 已${enabled ? '启用' : '禁用'}`);
        
        // 更新本地状态
        setServers(prevServers => 
          prevServers.map(server => 
            server.name === name ? { ...server, enabled } : server
          )
        );
        
        // 如果启用，获取最新状态
        if (enabled) {
          fetchServersStatus();
        } else {
          // 如果禁用，直接更新状态
          setServersStatus(prev => ({
            ...prev,
            [name]: { running: false }
          }));
        }
      } else {
        MessageService.error(result.message || `${enabled ? '启用' : '禁用'}MCP服务器失败`);
      }
    } catch (error) {
      console.error(`${enabled ? '启用' : '禁用'}MCP服务器失败:`, error);
      MessageService.error(`${enabled ? '启用' : '禁用'}MCP服务器失败`);
    } finally {
      setLoading(false);
    }
  };

  // 修改MCP服务器
  const handleEditServer = async (values) => {
    setLoading(true);
    console.log('values', values);
    try {
      const config = values.type === 'stdio' 
        ? { 
            command: values.command,
            args: values.args ? values.args.split(/[\s\n]+/).filter(arg => arg.trim()) : [],
            env: values.env ? values.env.split(/[\s\n]+/).filter(env => env.trim()) : {}
          }
        : { 
            url: values.url 
          };
      const result = await updateMCPServer(currentServer.name, values.type, config, values.description,values.name);
      
      if (result.success) {
        MessageService.success(`MCP服务器 ${currentServer.name} 已更新`);
        setEditModalVisible(false);
        editForm.resetFields();
        fetchServers();
      } else {
        MessageService.error(result.message || '更新MCP服务器失败');
      }
    } catch (error) {
      console.error('更新MCP服务器失败:', error);
      MessageService.error('更新MCP服务器失败');
    } finally {
      setLoading(false);
    }
  };

  // 打开编辑模态框
  const showEditModal = (server) => {
    setCurrentServer(server);
    setEditServerType(server.type);
    
    // 设置表单初始值
    const initialValues = {
      type: server.type,
      name: server.name
    };
    
    if (server.type === 'stdio') {
      initialValues.command = server.config.command || '';
      initialValues.args = Array.isArray(server.config.args) 
        ? server.config.args.join('\n') 
        : '';
      initialValues.env = Array.isArray(server.config.env) 
        ? server.config.env.join('\n') 
        : '';
      initialValues.description = server.config.description || '';
    } else if (server.type === 'sse') {
      initialValues.url = server.config.url || '';
      initialValues.description = server.config.description || '';
    }
    
    editForm.setFieldsValue(initialValues);
    setEditModalVisible(true);
  };

  // DXT上传成功回调
  const handleDXTUploadSuccess = async (extensionData) => {
    MessageService.success(`DXT扩展 "${extensionData.name}" 上传成功！`);
    setDxtUploadVisible(false);
    setDxtRefreshTrigger(prev => prev + 1);
    // 刷新MCP服务器列表以显示新安装的扩展
    await fetchServers();
  };
  
  // DXT上传错误回调
  const handleDXTUploadError = (errorMessage) => {
    MessageService.error(`DXT扩展上传失败: ${errorMessage}`);
  };
  
  // DXT扩展变更回调
  const handleDXTExtensionChange = async () => {
    setDxtRefreshTrigger(prev => prev + 1);
    await fetchServers();
  };

  // DXT扩展重新配置函数
  const handleReconfigureDXT = async (extensionName) => {
    console.log('handleReconfigureDXT 被调用，扩展名:', extensionName);

    try {
      // 获取扩展信息
      const response = await fetch(`/api/dxt/info/${extensionName}`);
      const result = await response.json();

      if (result.success) {
        const extensionInfo = result.data;
        // 检查是否有用户配置需求
        if (extensionInfo.manifest && extensionInfo.manifest.user_config) {
          // 打开重新配置模态框
          setReconfigureModalVisible(true);
          setReconfigureExtensionName(extensionName);
          setReconfigureManifest(extensionInfo.manifest);

          // 获取当前配置值
          const currentConfig = extensionInfo.current_config || {};
          setReconfigureCurrentConfig(currentConfig);

          // 初始化表单
          const initialValues = {};
          Object.keys(extensionInfo.manifest.user_config).forEach(varName => {
            const configDef = extensionInfo.manifest.user_config[varName];
            if (currentConfig[varName] !== undefined) {
              initialValues[varName] = currentConfig[varName];
            } else if ('default' in configDef) {
              initialValues[varName] = configDef.default;
            }
          });
          reconfigureForm.setFieldsValue(initialValues);
        } else {
          MessageService.info('此扩展无需配置');
        }
      } else {
        MessageService.error(`获取扩展信息失败: ${result.message}`);
      }
    } catch (error) {
      console.error('重新配置DXT扩展错误:', error);
      MessageService.error('重新配置失败，请检查网络连接');
    }
  };

  // 提交重新配置
  const handleReconfigureSubmit = async () => {
    try {
      const values = await reconfigureForm.validateFields();

      // 调用重新配置API
      const response = await fetch(`/api/dxt/reconfigure/${reconfigureExtensionName}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_config: values
        })
      });

      const result = await response.json();

      if (result.success) {
        MessageService.success('扩展配置更新成功！');
        setReconfigureModalVisible(false);
        // 刷新服务器列表
        await fetchServers();
      } else {
        MessageService.error(`配置更新失败: ${result.message}`);
      }
    } catch (error) {
      console.error('重新配置扩展错误:', error);
      MessageService.error('配置更新失败，请检查网络连接');
    }
  };

  // 取消重新配置
  const handleReconfigureCancel = () => {
    setReconfigureModalVisible(false);
    setReconfigureExtensionName('');
    setReconfigureManifest(null);
    setReconfigureCurrentConfig({});
    reconfigureForm.resetFields();
  };

  // 根据配置定义渲染表单控件
  const renderReconfigureInput = (varName, configDef) => {
    const { type = 'string', title, description, sensitive = false, min, max, required = false } = configDef;

    // 构建验证规则
    const rules = [];
    if (required) {
      rules.push({ required: true, message: `请输入 ${title || varName}` });
    }

    if (type === 'number' && (min !== undefined || max !== undefined)) {
      rules.push({
        type: 'number',
        min: min,
        max: max,
        message: `值必须在 ${min || '无限制'} 到 ${max || '无限制'} 之间`
      });
    }

    // 根据类型渲染不同的输入控件
    let inputComponent;
    switch (type) {
      case 'number':
        inputComponent = (
          <Input
            type="number"
            placeholder={`请输入 ${title || varName}`}
            min={min}
            max={max}
          />
        );
        break;

      case 'boolean':
        inputComponent = (
          <Switch
            checkedChildren="是"
            unCheckedChildren="否"
          />
        );
        // 布尔类型不需要必填验证
        rules.splice(0, 1);
        break;

      default: // string, directory, file
        inputComponent = (
          <Input
            type={sensitive ? 'password' : 'text'}
            placeholder={`请输入 ${title || varName}`}
            autoComplete="off"
          />
        );
        break;
    }

    return (
      <Form.Item
        key={varName}
        name={varName}
        label={
          <Space>
            <span>{title || varName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
            {required && <Tag color="red" size="small">必需</Tag>}
            {sensitive && <Tag color="orange" size="small">敏感</Tag>}
          </Space>
        }
        help={description}
        rules={rules}
        valuePropName={type === 'boolean' ? 'checked' : 'value'}
      >
        {inputComponent}
      </Form.Item>
    );
  };

  // DXT扩展卸载函数
  const handleUninstallDXT = async (extensionName) => {
    console.log('handleUninstallDXT 被调用，扩展名:', extensionName);

    modal.confirm({
      title: '确认卸载',
      icon: <ExclamationCircleOutlined />,
      content: `确定要卸载DXT扩展 ${extensionName} 吗？`,
      okText: '卸载',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        setLoading(true);
        try {
          console.log('开始卸载DXT扩展:', extensionName);
          const response = await fetch(`/api/dxt/uninstall-from-mcp/${extensionName}`, {
            method: 'DELETE',
          });

          console.log('卸载API响应状态:', response.status);
          const result = await response.json();
          console.log('卸载API响应结果:', result);

          if (result.success) {
            MessageService.success(`DXT扩展 ${extensionName} 已卸载`);
            await fetchServers();
          } else {
            MessageService.error(result.message || '卸载DXT扩展失败');
          }
        } catch (error) {
          console.error('卸载DXT扩展失败:', error);
          MessageService.error('卸载DXT扩展失败: ' + error.message);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // 检测服务器来源类型
  const _detectServerSource = (record) => {
    // 根据配置特征判断来源
    //console.log('检测服务器来源:', record.name, 'config.source:', record.config?.source);
    if (record.config && record.config.source === 'dxt') {
      //console.log('检测到DXT扩展:', record.name);
      return 'DXT';
    } else if (record.config && (record.config._marketplaceSource || record.config.source === 'marketplace')) {
      return 'Marketplace';
    } else {
      return 'Manual';
    }
  };

  // 获取来源标签颜色
  const _getSourceColor = (source) => {
    switch (source) {
      case 'DXT': return 'orange';
      case 'Marketplace': return 'blue';
      case 'Manual': return 'default';
      default: return 'default';
    }
  };

  // 获取来源图标
  const _getSourceIcon = (source) => {
    switch (source) {
      case 'DXT': return <FileAddOutlined />;
      case 'Marketplace': return <AppstoreOutlined />;
      case 'Manual': return <EditOutlined />;
      default: return null;
    }
  };

  // 服务器表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 140,
      render: (name) => (
        <Text strong>{name}</Text>
      ),
    },
    {
      title: '来源',
      key: 'source',
      width: 80,
      render: (_, record) => {
        const source = _detectServerSource(record);
        return (
          <Tag 
            color={_getSourceColor(source)} 
            icon={_getSourceIcon(source)}
            className="source-tag"
          >
            {source}
          </Tag>
        );
      },
    },
    {
      title: '描述',
      key: 'description',
      width: 220,
      render: (_, record) => {
        const description = record.config?.description || '-';
        return (
          <Text type="secondary" style={{ fontSize: '13px' }}>
            {description}
          </Text>
        );
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 70,
      render: (type) => (
        <Tag color={type === 'stdio' ? 'blue' : 'green'}>
          {type === 'stdio' ? 'Stdio' : 'SSE'}
        </Tag>
      ),
    },
    {
      title: '状态',
      key: 'status',
      width: 90,
      render: (_, record) => {
        const status = serversStatus[record.name];
        //console.log('status',status);
        const isRunning = status?.running === true;
        const isEnabled = record.enabled !== false;
        
        if (!isEnabled) {
          return <Badge status="default" text="已禁用" />;
        } else if (isRunning) {
          return <Badge status="success" text="运行中" />;
        } else {
          return <Badge status="error" text="未运行" />;
        }
      },
    },
    {
      title: '配置',
      dataIndex: 'config',
      key: 'config',
      render: (config, record) => {
        if (config.command) {
          return (
            <Space direction="vertical" size="small" style={{ fontSize: '12px', width: '100%' }}>
              <div style={{ wordBreak: 'break-all' }}>
                <Text type="secondary">命令:</Text> <Text code>{config.command}</Text>
              </div>
              {config.args && config.args.length > 0 && (
                <div style={{ wordBreak: 'break-all' }}>
                  <Text type="secondary">参数:</Text> 
                  <div style={{ display: 'inline' }}>
                    {Array.isArray(config.args) && config.args.map((arg, index) => (
                      <Text key={index} code style={{ marginRight: 4, marginBottom: 2, display: 'inline-block' }}>
                        {arg}
                      </Text>
                    ))}
                  </div>
                </div>
              )}
              {config.env && Object.keys(config.env).length > 0 && (
                <div>
                  <Text type="secondary">环境:</Text> 
                  {Object.entries(config.env).map(([key, value], index) => (
                    <Tooltip key={key} title={`${key}=${value || '***'}`}>
                      <Text code style={{ cursor: 'help', marginRight: 4, marginBottom: 2, display: 'inline-block' }}>
                        {key}
                      </Text>
                    </Tooltip>
                  ))}
                </div>
              )}
            </Space>
          );
        } else {
          return (
            <div style={{ fontSize: '12px', wordBreak: 'break-all' }}>
              <Text type="secondary">URL:</Text> <Text code>{config.url}</Text>
            </div>
          );
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => {
        const serverSource = _detectServerSource(record);
        const isDXTSource = serverSource === 'DXT';
       // console.log('服务器操作按钮:', record.name, 'source:', serverSource, 'isDXT:', isDXTSource);
        
        return (
          <Space size="small">
            <Switch
              checked={record.enabled !== false}
              onChange={(checked) => handleToggleServer(record.name, checked)}
              loading={loading}
              checkedChildren="启用"
              unCheckedChildren="禁用"
            />
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => isDXTSource ? handleReconfigureDXT(record.name) : showEditModal(record)}
              title={isDXTSource ? "重新配置扩展" : "编辑服务器"}
            >
              {isDXTSource ? '配置' : '编辑'}
            </Button>
            <Button 
              type="text" 
              size="small"
              danger 
              icon={<DeleteOutlined />} 
              onClick={() => isDXTSource ? handleUninstallDXT(record.name) : handleDeleteServer(record.name)}
              title={isDXTSource ? "卸载扩展" : "删除服务器"}
            >
              {isDXTSource ? '卸载' : '删除'}
            </Button>
          </Space>
        );
      },
    },
  ];

  // 渲染表单
  const renderForm = () => {
    return (
      <Form
        form={form}
        layout="vertical"
        onFinish={handleAddServer}
        initialValues={{
          type: serverType
        }}
      >
        <Form.Item
          name="name"
          label="服务器名称"
          rules={[{ required: true, message: '请输入服务器名称' }]}
        >
          <Input placeholder="输入服务器名称" />
        </Form.Item>
        
        <Form.Item
          name="type"
          label="服务器类型"
          rules={[{ required: true, message: '请选择服务器类型' }]}
        >
          <Select onChange={setServerType}>
            <Option value="stdio">标准输入/输出 (Stdio)</Option>
            <Option value="sse">Server-Sent Events (SSE)</Option>
          </Select>
        </Form.Item>
        
        {serverType === 'stdio' && (
          <>
            <Form.Item
              name="command"
              label="命令"
              rules={[{ required: true, message: '请输入命令' }]}
            >
              <Input placeholder="输入命令 (例如: python, node)" />
            </Form.Item>
            
            <Form.Item
              name="args"
              label="参数"
              extra="每行一个参数"
            >
              <TextArea 
                placeholder="输入参数，每行一个 (例如: script.py)" 
                autoSize={{ minRows: 2, maxRows: 6 }}
              />
            </Form.Item>
            
            <Form.Item
              name="env"
              label="环境变量"
              extra="每行一个环境变量 (格式: KEY=VALUE)"
            >
              <TextArea 
                placeholder="输入环境变量，每行一个 (例如: PORT=3000)" 
                autoSize={{ minRows: 2, maxRows: 6 }}
              />
            </Form.Item>
          </>
        )}
        
        {serverType === 'sse' && (
          <Form.Item
            name="url"
            label="服务器URL"
            rules={[{ required: true, message: '请输入服务器URL' }]}
          >
            <Input placeholder="输入服务器URL (例如: http://localhost:3000/sse)" />
          </Form.Item>
        )}
        
        <Form.Item
          name="description"
          label="描述"
          extra="服务器的用途或其他说明信息"
        >
          <TextArea 
            placeholder="输入服务器描述（可选）" 
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
      </Form>
    );
  };

  // 渲染编辑表单
  const renderEditForm = () => {
    if (!currentServer) return null;
    
    return (
      <Form
        form={editForm}
        layout="vertical"
        onFinish={handleEditServer}
      >
        <Form.Item
          name="name"
          label="服务器名称"
          rules={[{ required: true, message: '请输入服务器名称' }]}
        >
          <Input placeholder="输入服务器名称" />
        </Form.Item>
        
        <Form.Item
          name="type"
          label="服务器类型"
          rules={[{ required: true, message: '请选择服务器类型' }]}
        >
          <Select onChange={setEditServerType}>
            <Option value="stdio">标准输入/输出 (Stdio)</Option>
            <Option value="sse">Server-Sent Events (SSE)</Option>
          </Select>
        </Form.Item>
        
        {editServerType === 'stdio' && (
          <>
            <Form.Item
              name="command"
              label="命令"
              rules={[{ required: true, message: '请输入命令' }]}
            >
              <Input placeholder="输入命令 (例如: python, node)" />
            </Form.Item>
            
            <Form.Item
              name="args"
              label="参数"
              extra="每行一个参数"
            >
              <TextArea 
                placeholder="输入参数，每行一个 (例如: script.py)" 
                autoSize={{ minRows: 2, maxRows: 6 }}
              />
            </Form.Item>
            
            <Form.Item
              name="env"
              label="环境变量"
              extra="每行一个环境变量 (格式: KEY=VALUE)"
            >
              <TextArea 
                placeholder="输入环境变量，每行一个 (例如: PORT=3000)" 
                autoSize={{ minRows: 2, maxRows: 6 }}
              />
            </Form.Item>
          </>
        )}
        
        {editServerType === 'sse' && (
          <Form.Item
            name="url"
            label="服务器URL"
            rules={[{ required: true, message: '请输入服务器URL' }]}
          >
            <Input placeholder="输入服务器URL (例如: http://localhost:3000/sse)" />
          </Form.Item>
        )}
        
        <Form.Item
          name="description"
          label="描述"
          extra="服务器的用途或其他说明信息"
        >
          <TextArea 
            placeholder="输入服务器描述（可选）" 
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
      </Form>
    );
  };

  // 加载MCP配置
  const loadMcpConfig = async () => {
    setConfigLoading(true);
    setConfigError(null);
    
    try {
      const response = await fetch('/api/mcp-config');
      
      if (!response.ok) {
        throw new Error(`加载失败 (${response.status}): ${response.statusText}`);
      }
      
      const data = await response.json();
      setMcpConfig(JSON.stringify(data, null, 2));
      setConfigValid(true);
      MessageService.success('MCP配置加载成功');
    } catch (error) {
      const errorMsg = '加载MCP配置失败: ' + (error.message || '未知错误');
      setConfigError(errorMsg);
      MessageService.error(errorMsg);
      console.error('加载MCP配置失败:', error);
    } finally {
      setConfigLoading(false);
    }
  };

  // 验证JSON格式
  const validateMcpConfig = (jsonString) => {
    setConfigValidating(true);
    setConfigError(null);
    
    try {
      JSON.parse(jsonString);
      setConfigValid(true);
      return true;
    } catch (error) {
      const errorMessage = `JSON格式错误: ${error.message}`;
      setConfigError(errorMessage);
      setConfigValid(false);
      return false;
    } finally {
      setConfigValidating(false);
    }
  };

  // 处理编辑器内容变化
  const handleConfigChange = (newValue) => {
    setMcpConfig(newValue);
    // 在用户停止输入后验证JSON
    clearTimeout(window.validateJsonTimeout);
    window.validateJsonTimeout = setTimeout(() => {
      validateMcpConfig(newValue);
    }, 800);
  };

  // 保存MCP配置
  const saveMcpConfig = async () => {
    // 先验证JSON格式
    if (!validateMcpConfig(mcpConfig)) {
      MessageService.error('无法保存，请先修复JSON格式错误');
      return;
    }
    
    setConfigSaving(true);
    setConfigError(null);
    
    try {
      const config = JSON.parse(mcpConfig);
      
      const response = await fetch('/api/mcp-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`保存失败 (${response.status}): ${errorText || response.statusText}`);
      }
      
      // 使用带图标的消息提示
      MessageService.success({
        content: 'MCP配置已成功保存',
        icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />
      });
      
      setMcpConfigVisible(false);
      
      if (onUpdateMcpConfig) {
        onUpdateMcpConfig();
      }
      
      fetchServers();
    } catch (error) {
      const errorMsg = '保存MCP配置失败: ' + (error.message || '未知错误');
      setConfigError(errorMsg);
      // 使用带图标的消息提示
      MessageService.error({
        content: errorMsg,
        icon: <WarningOutlined style={{ color: '#ff4d4f' }} />
      });
      console.error('保存MCP配置失败:', error);
    } finally {
      setConfigSaving(false);
    }
  };

  return (
    <div className="mcp-settings">
      {/* 添加contextHolder到组件树中 */}
      {contextHolder}
      
      <div className="mcp-settings-header">
        <Title level={4}>MCP服务器设置</Title>
        <Space>
          <Button
            icon={<AppstoreOutlined />}
            onClick={() => setMarketplaceVisible(true)}
          >
            插件市场
          </Button>
          <Button
            type="default"
            icon={<FileAddOutlined />}
            onClick={() => setDxtUploadVisible(true)}
          >
            上传DXT扩展
          </Button>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => {
              loadMcpConfig();
              setMcpConfigVisible(true);
            }}
          >
            编辑MCP配置文件
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              form.resetFields();
              setModalVisible(true);
            }}
          >
            添加服务器
          </Button>
        </Space>
      </div>
      
      <Divider />
      
      <Text type="secondary" className="mcp-settings-description">
        MCP (Model Context Protocol) 允许AI模型访问系统资源和执行特定操作。
        添加MCP服务器后，AI会话中的模型将能够使用这些服务器提供的工具。
        <br /><br />
        <strong>服务器类型说明：</strong>
        <br />
        - <strong>Stdio类型</strong>: 通过标准输入输出与子进程通信，适用于本地运行的MCP服务器。
        <br />
        - <strong>SSE类型</strong>: 通过Server-Sent Events与远程服务器通信，适用于远程运行的MCP服务器。
        <br /><br />
        配置将保存在 <code>~/./mcp.json</code> 文件中。
      </Text>
      
      <div className="mcp-settings-table">
        <Table
          columns={columns}
          dataSource={servers}
          rowKey="name"
          loading={loading}
          className="mcp-settings-table"
          size="middle"
          pagination={false}
          bordered={false}
          locale={{
            emptyText: (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无MCP服务器"
              >
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => {
                    form.resetFields();
                    setModalVisible(true);
                  }}
                >
                  添加第一个服务器
                </Button>
              </Empty>
            )
          }}
        />
      </div>
      
      <Modal
        title="添加MCP服务器"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        okText="添加"
        cancelText="取消"
        onOk={() => form.submit()}
        confirmLoading={loading}
      >
        {renderForm()}
      </Modal>
      
      <Modal
        title={`编辑MCP服务器 - ${currentServer?.name}`}
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        okText="保存"
        cancelText="取消"
        onOk={() => editForm.submit()}
        confirmLoading={loading}
      >
        {renderEditForm()}
      </Modal>

      {/* DXT上传Modal */}
      <Modal
        title="上传DXT扩展"
        open={dxtUploadVisible}
        onCancel={() => setDxtUploadVisible(false)}
        footer={null}
        width={600}
      >
        <DXTUpload
          onUploadSuccess={handleDXTUploadSuccess}
          onUploadError={handleDXTUploadError}
          disabled={loading}
        />
      </Modal>

      {/* 新增MCP配置编辑Modal */}
      <Modal
        title={
          <Space>
            <span>编辑MCP配置</span>
            {configLoading && <Spin indicator={<LoadingOutlined spin />} />}
            {configValid && !configLoading && <Badge status="success" text="格式正确" />}
          </Space>
        }
        open={mcpConfigVisible}
        onOk={saveMcpConfig}
        onCancel={() => setMcpConfigVisible(false)}
        width={800}
        okText={configSaving ? "保存中..." : "保存"}
        cancelText="取消"
        okButtonProps={{ 
          icon: configSaving ? <LoadingOutlined /> : <SaveOutlined />,
          disabled: configSaving || configLoading || !configValid,
          loading: configSaving
        }}
      >
        {configError && (
          <Alert
            message="错误"
            description={configError}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
            className="mcp-config-error"
          />
        )}
        
        <Spin spinning={configLoading} tip="加载配置中..." className="mcp-config-loading">
          <div className="mcp-config-editor">
            <MonacoEditor
              value={mcpConfig}
              onChange={handleConfigChange}
              language="json"
              height="500px"
              options={{
                minimap: { enabled: true },
                lineNumbers: "on",
                scrollBeyondLastLine: false,
                automaticLayout: true
              }}
            />
          </div>
        </Spin>
        
        <div className="mcp-config-status-bar">
          <Text type="secondary">
            提示: 配置保存后将立即生效，请确保JSON格式正确
          </Text>
          <Space>
            {configValidating && <Spin size="small" />}
            {configValid && !configValidating && (
              <span className="mcp-config-valid">
                <Text>JSON格式有效</Text>
                <CheckCircleOutlined />
              </span>
            )}
            {!configValid && !configValidating && (
              <span className="mcp-config-invalid">
                <Text>JSON格式无效</Text>
                <WarningOutlined />
              </span>
            )}
          </Space>
        </div>
      </Modal>
      
      {/* 插件市场弹窗 */}
      <MCPMarketplace
        visible={marketplaceVisible}
        onClose={() => {
          setMarketplaceVisible(false);
          fetchServers(); // 关闭后刷新服务器列表
        }}
        installedServers={servers.reduce((acc, server) => {
          acc[server.name] = server;
          return acc;
        }, {})}
      />
    </div>
  );
};

export default MCPSettings;